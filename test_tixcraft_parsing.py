#!/usr/bin/env python3
"""
測試 TixCraft 爬蟲的票券解析功能
"""

import sys
import os
import re
from typing import Optional

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from monitors.ticket_crawler.sites.base import TicketArea, TicketStatus

def test_parse_ticket_area_text(text: str) -> Optional[TicketArea]:
    """
    測試票券區域文本解析
    """
    try:
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 模式1: "橙 ($207) - 240016張" 格式
        # 匹配: 區域名稱 + ($價格) + - + 數字張
        pattern1 = r'([^($]+?)\s*\(\$(\d+)\)\s*-\s*(\d+)張'
        match1 = re.search(pattern1, text)
        if match1:
            area_name = match1.group(1).strip()
            price = int(match1.group(2))
            full_number = match1.group(3)

            # 處理類似 "240016" 的格式，其中 "2400" 是價格相關，"16" 是實際票數
            # 如果數字很大（>1000），通常後面1-3位是實際票數
            if len(full_number) >= 5:
                # 取最後2位作為票數
                available = int(full_number[-2:])
            elif len(full_number) == 4:
                # 取最後1位作為票數
                available = int(full_number[-1:])
            else:
                # 直接使用整個數字
                available = int(full_number)

            return TicketArea(
                name=area_name,
                price=price,
                status=TicketStatus.AVAILABLE,
                available_count=available
            )

        # 模式2: "橙509區 ($800)" 售完格式
        pattern2 = r'([^($]+?)\s*\(\$(\d+)\)(?:\s*-\s*)?(?:已售完|售完|Sold out|$)'
        match2 = re.search(pattern2, text, re.IGNORECASE)
        if match2 and any(keyword in text for keyword in ['售完', 'sold out', '已售完']):
            area_name = match2.group(1).strip()
            price = int(match2.group(2))

            return TicketArea(
                name=area_name,
                price=price,
                status=TicketStatus.SOLD_OUT,
                available_count=0
            )

        # 模式3: "狂歡人生站區 ($2800) - 18張" 特殊區域格式
        pattern3 = r'([^($]+?站區)\s*\(\$(\d+)\)\s*-\s*(\d+)張'
        match3 = re.search(pattern3, text)
        if match3:
            area_name = match3.group(1).strip()
            price = int(match3.group(2))
            available = int(match3.group(3))

            return TicketArea(
                name=area_name,
                price=price,
                status=TicketStatus.AVAILABLE,
                available_count=available
            )

        return None

    except Exception as e:
        print(f"Error parsing ticket area text '{text}': {e}")
        return None

def main():
    """測試主函數"""
    print("測試 TixCraft 票券解析功能")
    print("=" * 50)
    
    # 測試用例
    test_cases = [
        "橙 ($207) - 240016張",
        "橙 ($208) - 24004張", 
        "紅 ($216) - 24004張",
        "紅 ($218) - 240018張",
        "紅 ($219) - 240058張",
        "紅 ($220) - 240058張",
        "狂歡人生站區 ($2800) - 18張",
        "橙509區 ($800)",
        "橙510區 ($800)",
        "橙509區 ($1500)",
        "橙510區 ($1500)",
        "橙511區 ($1500)",
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n測試 {i}: {test_text}")
        result = test_parse_ticket_area_text(test_text)
        
        if result:
            print(f"  ✅ 解析成功:")
            print(f"     區域名稱: {result.name}")
            print(f"     價格: ${result.price}")
            print(f"     狀態: {result.status.value}")
            print(f"     可用票數: {result.available_count}")
        else:
            print(f"  ❌ 解析失敗")
    
    print("\n" + "=" * 50)
    print("測試完成")

if __name__ == "__main__":
    main()
