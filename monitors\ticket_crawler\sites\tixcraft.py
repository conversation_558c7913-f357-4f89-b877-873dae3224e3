"""
Tixcraft (拓元售票) 爬蟲實現
"""

import re
import asyncio
import logging
import aiohttp
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup

from monitors.ticket_crawler.sites.base import (
    BaseCrawler, CrawlResult, EventInfo, TicketArea, TicketStatus
)
from monitors.ticket_crawler.utils.anti_detection import AntiDetectionMixin
from monitors.ticket_crawler.utils.parsers import DataParser

logger = logging.getLogger('monitors.ticket_crawler.tixcraft')

class TixcraftCrawler(BaseCrawler, AntiDetectionMixin):
    """
    Tixcraft 票務網站爬蟲
    支持監控 tixcraft.com 的票券可用性
    """
    
    def __init__(self, **kwargs):
        BaseCrawler.__init__(
            self,
            site_name="Tixcraft",
            base_url="https://tixcraft.com",
            **kwargs
        )
        AntiDetectionMixin.__init__(self)

        # Tixcraft 特定配置
        self.activity_base_url = "https://tixcraft.com/activity/detail/"
        self.ticket_base_url = "https://tixcraft.com/ticket/area/"

        # HTTP session
        self.session = None
        
    def extract_event_id(self, event_url: str) -> Optional[str]:
        """
        從 Tixcraft URL 中提取活動ID
        
        支持的URL格式：
        - https://tixcraft.com/activity/detail/25_xalive
        - https://tixcraft.com/ticket/area/25_xalive/19055
        
        Args:
            event_url: 活動URL
            
        Returns:
            str: 活動ID（如 "25_xalive"）
        """
        patterns = [
            r'/activity/detail/([^/?]+)',
            r'/ticket/area/([^/?]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, event_url)
            if match:
                return match.group(1)
        
        return None
    
    def is_valid_url(self, url: str) -> bool:
        """
        檢查是否為有效的 Tixcraft URL
        
        Args:
            url: 要檢查的URL
            
        Returns:
            bool: 是否有效
        """
        if not url:
            return False
            
        parsed = urlparse(url)
        if parsed.netloc != 'tixcraft.com':
            return False
            
        # 檢查路徑格式
        valid_patterns = [
            r'/activity/detail/[^/?]+',
            r'/ticket/area/[^/?]+',
        ]
        
        return any(re.search(pattern, parsed.path) for pattern in valid_patterns)
    
    def _convert_to_ticket_url(self, activity_url: str) -> str:
        """
        將活動詳情URL轉換為票券選擇URL
        
        Args:
            activity_url: 活動詳情URL
            
        Returns:
            str: 票券選擇URL
        """
        event_id = self.extract_event_id(activity_url)
        if not event_id:
            return activity_url
            
        # 如果已經是票券URL，直接返回
        if '/ticket/area/' in activity_url:
            return activity_url
            
        # 轉換為票券URL（需要session_id，這裡先用空字符串）
        return f"{self.ticket_base_url}{event_id}/"
    
    async def crawl_event(self, event_url: str) -> CrawlResult:
        """
        爬取 Tixcraft 活動的票券信息
        
        Args:
            event_url: 活動URL
            
        Returns:
            CrawlResult: 爬取結果
        """
        try:
            # 驗證URL
            if not self.is_valid_url(event_url):
                return CrawlResult(
                    success=False,
                    error_message=f"Invalid Tixcraft URL: {event_url}"
                )
            
            # 提取活動ID
            event_id = self.extract_event_id(event_url)
            if not event_id:
                return CrawlResult(
                    success=False,
                    error_message=f"Cannot extract event ID from URL: {event_url}"
                )
            
            self.logger.info(f"Starting to crawl Tixcraft event: {event_id}")
            
            # 首先嘗試獲取活動基本信息
            event_info = await self._get_event_info(event_url, event_id)
            
            # 然後獲取票券信息
            ticket_areas = await self._get_ticket_areas(event_url, event_id)
            
            return CrawlResult(
                success=True,
                event_info=event_info,
                ticket_areas=ticket_areas
            )
            
        except Exception as e:
            self.logger.error(f"Error crawling Tixcraft event {event_url}: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e)
            )
    
    async def _get_session(self):
        """獲取或創建 HTTP session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def _close_session(self):
        """關閉 HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _fetch_page(self, url: str) -> Optional[str]:
        """
        獲取網頁內容

        Args:
            url: 要獲取的URL

        Returns:
            str: 網頁HTML內容，失敗時返回None
        """
        session = await self._get_session()

        # 減少反爬蟲延遲以提高速度
        await self.random_delay(min_seconds=0.5, max_seconds=1.5)

        # 獲取隨機化的請求頭
        headers = self.get_random_headers({
            'Referer': 'https://tixcraft.com/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        })

        for attempt in range(self.max_retries):
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.debug(f"Successfully fetched {url}")
                        return content
                    elif response.status == 429:
                        # 被限流，等待較短時間
                        wait_time = min(2 ** attempt, 5)  # 最多等待5秒
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")

            except asyncio.TimeoutError:
                logger.warning(f"Timeout on attempt {attempt + 1} for {url}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(0.5 * (attempt + 1))  # 減少重試延遲
            except Exception as e:
                logger.error(f"Error fetching {url} on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(0.5 * (attempt + 1))  # 減少重試延遲

        return None

    async def _get_event_info(self, event_url: str, event_id: str) -> EventInfo:
        """
        獲取活動基本信息

        Args:
            event_url: 活動URL
            event_id: 活動ID

        Returns:
            EventInfo: 活動信息
        """
        try:
            # 嘗試從票券頁面獲取信息
            html_content = await self._fetch_page(event_url)
            if not html_content:
                logger.warning(f"Failed to fetch event page: {event_url}")
                return EventInfo(
                    title=f"Tixcraft Event {event_id}",
                    date="TBD",
                    time="TBD",
                    venue="TBD",
                    url=event_url,
                    event_id=event_id
                )

            soup = BeautifulSoup(html_content, 'lxml')

            # 提取活動標題 - 改進解析邏輯
            title = "Unknown Event"
            title_selectors = [
                'h1', 'h2', '.event-title', '.activity-title',
                'title', '.page-title', '.main-title', '.title'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    raw_title = title_elem.get_text(strip=True)
                    if raw_title and len(raw_title) > 5:  # 確保標題有意義
                        # 清理標題
                        title = re.sub(r'\s*-\s*拓元售票網.*$', '', raw_title)
                        title = re.sub(r'\s*\|\s*TIXCRAFT.*$', '', title)
                        title = re.sub(r'\s*-\s*TIXCRAFT.*$', '', title)
                        title = title.strip()
                        if title:
                            break

            # 從 URL 或頁面內容中提取更多信息
            if title == "Unknown Event":
                # 嘗試從頁面文本中提取標題
                page_text = soup.get_text()
                # 尋找演唱會、音樂會等關鍵字附近的文本
                concert_patterns = [
                    r'([^。\n]{5,50}(?:演唱會|音樂會|演出|表演|活動))',
                    r'([\w\s]{5,50}(?:Concert|Live|Show))',
                ]
                for pattern in concert_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        title = match.group(1).strip()
                        break

            # 提取日期時間信息 - 改進解析
            date = "待公布"
            time = "待公布"

            # 更廣泛的日期選擇器
            date_selectors = [
                '.event-date', '.activity-date', '.datetime',
                '.show-time', '.performance-time', '.date',
                '.time', '.schedule', '.when'
            ]

            date_time_text = ""
            for selector in date_selectors:
                date_elem = soup.select_one(selector)
                if date_elem:
                    date_time_text = date_elem.get_text(strip=True)
                    if date_time_text and len(date_time_text) > 3:
                        break

            # 如果沒找到，嘗試從頁面文本中提取日期
            if not date_time_text:
                page_text = soup.get_text()
                date_patterns = [
                    r'(\d{4}[年/-]\d{1,2}[月/-]\d{1,2}[日]?)',
                    r'(\d{1,2}[月/-]\d{1,2}[日]?\s*\d{4}[年]?)',
                    r'(\d{4}-\d{2}-\d{2})',
                ]
                for pattern in date_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        date_time_text = match.group(1)
                        break

            # 解析日期時間
            if date_time_text:
                try:
                    parsed_dt = DataParser.parse_datetime(date_time_text)
                    if parsed_dt.date != "TBD":
                        date = parsed_dt.date
                    if parsed_dt.time != "TBD":
                        time = parsed_dt.time
                except:
                    # 如果解析失敗，保持原始文本
                    if "年" in date_time_text or "-" in date_time_text:
                        date = date_time_text

            # 提取場地信息 - 改進解析
            venue = "待公布"
            venue_selectors = [
                '.venue', '.location', '.event-venue',
                '.place', '.hall', '.address', '.where'
            ]

            for selector in venue_selectors:
                venue_elem = soup.select_one(selector)
                if venue_elem:
                    venue_text = venue_elem.get_text(strip=True)
                    if venue_text and len(venue_text) > 2:
                        venue = venue_text
                        break

            # 如果沒找到，嘗試從頁面文本中提取場地
            if venue == "待公布":
                page_text = soup.get_text()
                venue_patterns = [
                    r'([^。\n]{3,30}(?:巨蛋|體育館|展覽館|音樂廳|劇院|會館|中心))',
                    r'([^。\n]{3,30}(?:Arena|Hall|Center|Theatre|Stadium))',
                ]
                for pattern in venue_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        venue = match.group(1).strip()
                        break

            return EventInfo(
                title=title,
                date=date,
                time=time,
                venue=venue,
                url=event_url,
                event_id=event_id
            )

        except Exception as e:
            logger.error(f"Error parsing event info from {event_url}: {e}")
            return EventInfo(
                title=f"Tixcraft Event {event_id}",
                date="TBD",
                time="TBD",
                venue="TBD",
                url=event_url,
                event_id=event_id
            )
    
    async def _get_ticket_areas(self, event_url: str, event_id: str) -> List[TicketArea]:
        """
        獲取票券區域信息

        Args:
            event_url: 活動URL
            event_id: 活動ID

        Returns:
            List[TicketArea]: 票券區域列表
        """
        try:
            html_content = await self._fetch_page(event_url)
            if not html_content:
                logger.warning(f"Failed to fetch ticket page: {event_url}")
                return []

            soup = BeautifulSoup(html_content, 'lxml')
            ticket_areas = []

            # 查找票券區域信息
            # Tixcraft 的票券信息通常在表格或列表中
            area_selectors = [
                'tr', 'li', '.ticket-area', '.area-item',
                '.section-item', 'div'
            ]

            for selector in area_selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if not text:
                        continue

                    # 檢查是否包含票券信息的關鍵字
                    if any(keyword in text for keyword in ['區', 'seat', 'remaining', '剩餘', '已售完', 'Sold out']):
                        area = self._parse_ticket_area_text(text)
                        if area:
                            ticket_areas.append(area)

                # 如果找到票券信息就停止搜索
                if ticket_areas:
                    break

            # 去重和排序
            unique_areas = {}
            for area in ticket_areas:
                key = f"{area.name}_{area.price}"
                if key not in unique_areas:
                    unique_areas[key] = area

            result = list(unique_areas.values())
            result.sort(key=lambda x: (x.price, x.name))

            logger.info(f"Found {len(result)} ticket areas for {event_id}")
            return result

        except Exception as e:
            logger.error(f"Error parsing ticket areas from {event_url}: {e}")
            return []

    def _parse_ticket_area_text(self, text: str) -> Optional[TicketArea]:
        """
        解析票券區域文本

        修正解析邏輯：
        - "橙 ($207) - 240016張" -> 區域名稱: "橙", 價格: 207, 票數: 16
        - "狂歡人生站區 ($2800) - 18張" -> 區域名稱: "狂歡人生站區", 價格: 2800, 票數: 18

        Args:
            text: 包含票券信息的文本

        Returns:
            TicketArea: 解析出的票券區域，失敗時返回None
        """
        try:
            # 清理文本
            text = re.sub(r'\s+', ' ', text).strip()

            # 模式1: "橙 ($207) - 240016張" 格式
            # 匹配: 區域名稱 + ($價格) + - + 數字張
            pattern1 = r'([^($]+?)\s*\(\$(\d+)\)\s*-\s*(\d+)張'
            match1 = re.search(pattern1, text)
            if match1:
                area_name = match1.group(1).strip()
                price = int(match1.group(2))
                full_number = match1.group(3)

                # 處理類似 "240016" 的格式，其中 "2400" 是價格相關，"16" 是實際票數
                # 如果數字很大（>1000），通常後面1-3位是實際票數
                if len(full_number) >= 5:
                    # 取最後2位作為票數
                    available = int(full_number[-2:])
                elif len(full_number) == 4:
                    # 取最後1位作為票數
                    available = int(full_number[-1:])
                else:
                    # 直接使用整個數字
                    available = int(full_number)

                return TicketArea(
                    name=area_name,
                    price=price,
                    status=TicketStatus.AVAILABLE,
                    available_count=available
                )

            # 模式2: "橙509區 ($800)" 售完格式
            pattern2 = r'([^($]+?)\s*\(\$(\d+)\)(?:\s*-\s*)?(?:已售完|售完|Sold out|$)'
            match2 = re.search(pattern2, text, re.IGNORECASE)
            if match2 and any(keyword in text for keyword in ['售完', 'sold out', '已售完']):
                area_name = match2.group(1).strip()
                price = int(match2.group(2))

                return TicketArea(
                    name=area_name,
                    price=price,
                    status=TicketStatus.SOLD_OUT,
                    available_count=0
                )

            # 模式3: "狂歡人生站區 ($2800) - 18張" 特殊區域格式
            pattern3 = r'([^($]+?站區)\s*\(\$(\d+)\)\s*-\s*(\d+)張'
            match3 = re.search(pattern3, text)
            if match3:
                area_name = match3.group(1).strip()
                price = int(match3.group(2))
                available = int(match3.group(3))

                return TicketArea(
                    name=area_name,
                    price=price,
                    status=TicketStatus.AVAILABLE,
                    available_count=available
                )

            # 模式4: 舊格式兼容 "橙207區2400 剩餘 16"
            pattern4 = r'([^0-9]+?)(\d+).*?(\d{3,4}).*?剩餘\s*(\d+)'
            match4 = re.search(pattern4, text)
            if match4:
                area_name = match4.group(1).strip() + match4.group(2)
                price = int(match4.group(3))
                available = int(match4.group(4))

                return TicketArea(
                    name=area_name,
                    price=price,
                    status=TicketStatus.AVAILABLE,
                    available_count=available
                )

            # 模式5: "33 seat(s) remaining" 英文格式
            pattern5 = r'([^0-9]+?).*?(\d{3,4}).*?(\d+)\s+seat\(s\)\s+remaining'
            match5 = re.search(pattern5, text, re.IGNORECASE)
            if match5:
                area_name = match5.group(1).strip()
                price = int(match5.group(2))
                available = int(match5.group(3))

                return TicketArea(
                    name=area_name,
                    price=price,
                    status=TicketStatus.AVAILABLE,
                    available_count=available
                )

            return None

        except Exception as e:
            logger.debug(f"Error parsing ticket area text '{text}': {e}")
            return None
    
    def _parse_ticket_section(self, section_html: str) -> List[TicketArea]:
        """
        解析票券區域HTML
        
        Args:
            section_html: 區域HTML內容
            
        Returns:
            List[TicketArea]: 解析出的票券區域
        """
        ticket_areas = []
        
        # 解析價格區域標題（如 "2800區"）
        price_match = re.search(r'(\d+)區', section_html)
        base_price = int(price_match.group(1)) if price_match else 0
        
        # 解析各個具體區域
        # 匹配格式：區域名稱 + 狀態信息
        area_patterns = [
            r'([^<>\n]+?)\s+(\d+)\s+seat\(s\)\s+remaining',  # 有票
            r'([^<>\n]+?)\s+Sold\s+out',                      # 售完
        ]
        
        for pattern in area_patterns:
            matches = re.finditer(pattern, section_html, re.IGNORECASE)
            for match in matches:
                area_name = match.group(1).strip()
                
                if 'seat(s) remaining' in match.group(0):
                    # 有票
                    available_count = int(match.group(2))
                    status = TicketStatus.AVAILABLE
                else:
                    # 售完
                    available_count = 0
                    status = TicketStatus.SOLD_OUT
                
                ticket_areas.append(TicketArea(
                    name=area_name,
                    price=base_price,
                    status=status,
                    available_count=available_count
                ))
        
        return ticket_areas
    
    async def test_connection(self) -> bool:
        """
        測試與 Tixcraft 的連接

        Returns:
            bool: 連接是否正常
        """
        try:
            self.logger.info("Testing connection to Tixcraft...")

            # 測試訪問首頁
            test_url = "https://tixcraft.com/"
            html_content = await self._fetch_page(test_url)

            if html_content and "tixcraft" in html_content.lower():
                self.logger.info("Tixcraft connection test successful")
                return True
            else:
                self.logger.warning("Tixcraft connection test failed - unexpected response")
                return False

        except Exception as e:
            self.logger.error(f"Tixcraft connection test failed: {e}")
            return False
        finally:
            # 清理 session
            await self._close_session()

    async def __aenter__(self):
        """異步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        await self._close_session()
