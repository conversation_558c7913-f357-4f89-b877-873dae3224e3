"""
寬宏售票 (KHAM) 爬蟲實現
"""

import re
import asyncio
import logging
import aiohttp
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin, parse_qs
from bs4 import BeautifulSoup

from monitors.ticket_crawler.sites.base import (
    BaseCrawler, CrawlResult, EventInfo, TicketArea, TicketStatus
)
from monitors.ticket_crawler.utils.anti_detection import AntiDetectionMixin
from monitors.ticket_crawler.utils.parsers import DataParser

logger = logging.getLogger('monitors.ticket_crawler.kham')

class KhamCrawler(BaseCrawler, AntiDetectionMixin):
    """
    寬宏售票網站爬蟲
    支持監控 kham.com.tw 的票券可用性
    """
    
    def __init__(self, **kwargs):
        BaseCrawler.__init__(
            self,
            site_name="KHAM",
            base_url="https://kham.com.tw",
            **kwargs
        )
        AntiDetectionMixin.__init__(self)

        # 寬宏特定配置
        self.ticket_base_url = "https://kham.com.tw/application/UTK02/UTK0204_.aspx"

        # HTTP session
        self.session = None
        
    def extract_event_id(self, event_url: str) -> Optional[str]:
        """
        從寬宏URL中提取活動ID
        
        支持的URL格式：
        - https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK&PRODUCT_ID=P0T9WGF5
        
        Args:
            event_url: 活動URL
            
        Returns:
            str: 活動ID組合（如 "P0TEI9AK_P0T9WGF5"）
        """
        try:
            parsed = urlparse(event_url)
            query_params = parse_qs(parsed.query)
            
            performance_id = query_params.get('PERFORMANCE_ID', [None])[0]
            product_id = query_params.get('PRODUCT_ID', [None])[0]
            
            if performance_id and product_id:
                return f"{performance_id}_{product_id}"
            elif performance_id:
                return performance_id
                
        except Exception as e:
            logger.debug(f"Error extracting event ID from {event_url}: {e}")
        
        return None
    
    def is_valid_url(self, url: str) -> bool:
        """
        檢查是否為有效的寬宏URL
        
        Args:
            url: 要檢查的URL
            
        Returns:
            bool: 是否有效
        """
        if not url:
            return False
            
        parsed = urlparse(url)
        if parsed.netloc != 'kham.com.tw':
            return False
            
        # 檢查路徑格式
        if '/UTK0204_.aspx' not in parsed.path:
            return False
            
        # 檢查必要參數
        query_params = parse_qs(parsed.query)
        return 'PERFORMANCE_ID' in query_params
    
    async def crawl_event(self, event_url: str) -> CrawlResult:
        """
        爬取寬宏活動的票券信息
        
        Args:
            event_url: 活動URL
            
        Returns:
            CrawlResult: 爬取結果
        """
        try:
            # 驗證URL
            if not self.is_valid_url(event_url):
                return CrawlResult(
                    success=False,
                    error_message=f"Invalid KHAM URL: {event_url}"
                )
            
            # 提取活動ID
            event_id = self.extract_event_id(event_url)
            if not event_id:
                return CrawlResult(
                    success=False,
                    error_message=f"Cannot extract event ID from URL: {event_url}"
                )
            
            self.logger.info(f"Starting to crawl KHAM event: {event_id}")
            
            # 首先嘗試獲取活動基本信息
            event_info = await self._get_event_info(event_url, event_id)
            
            # 然後獲取票券信息
            ticket_areas = await self._get_ticket_areas(event_url, event_id)
            
            return CrawlResult(
                success=True,
                event_info=event_info,
                ticket_areas=ticket_areas
            )
            
        except Exception as e:
            self.logger.error(f"Error crawling KHAM event {event_url}: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e)
            )
    
    async def _get_session(self):
        """獲取或創建 HTTP session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def _close_session(self):
        """關閉 HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _fetch_page(self, url: str) -> Optional[str]:
        """
        獲取網頁內容

        Args:
            url: 要獲取的URL

        Returns:
            str: 網頁HTML內容，失敗時返回None
        """
        session = await self._get_session()

        # 減少反爬蟲延遲以提高速度
        await self.random_delay(min_seconds=0.8, max_seconds=2.0)

        # 獲取隨機化的請求頭
        headers = self.get_random_headers({
            'Referer': 'https://kham.com.tw/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        })

        for attempt in range(self.max_retries):
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.debug(f"Successfully fetched {url}")
                        return content
                    elif response.status == 429:
                        # 被限流，等待較短時間
                        wait_time = min(2 ** attempt, 8)  # 最多等待8秒
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")

            except asyncio.TimeoutError:
                logger.warning(f"Timeout on attempt {attempt + 1} for {url}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))
            except Exception as e:
                logger.error(f"Error fetching {url} on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))

        return None

    async def _get_event_info(self, event_url: str, event_id: str) -> EventInfo:
        """
        獲取活動基本信息

        Args:
            event_url: 活動URL
            event_id: 活動ID

        Returns:
            EventInfo: 活動信息
        """
        try:
            html_content = await self._fetch_page(event_url)
            if not html_content:
                logger.warning(f"Failed to fetch event page: {event_url}")
                return EventInfo(
                    title=f"KHAM Event {event_id}",
                    date="待公布",
                    time="待公布",
                    venue="待公布",
                    url=event_url,
                    event_id=event_id
                )

            soup = BeautifulSoup(html_content, 'lxml')

            # 提取活動標題
            title = "Unknown Event"
            title_selectors = [
                'h1', 'h2', '.event-title', '.activity-title',
                '.title', '.main-title'
            ]
            
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    raw_title = title_elem.get_text(strip=True)
                    if raw_title and len(raw_title) > 5:
                        title = raw_title.strip()
                        break
            
            # 如果沒找到標題，從頁面文本中提取
            if title == "Unknown Event":
                page_text = soup.get_text()
                # 尋找演唱會、音樂會等關鍵字附近的文本
                concert_patterns = [
                    r'([^。\n]{5,80}(?:演唱會|音樂會|演出|表演|活動|展覽))',
                    r'([\w\s]{5,80}(?:Concert|Live|Show|Exhibition))',
                ]
                for pattern in concert_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        title = match.group(1).strip()
                        break

            # 提取日期時間信息
            date = "待公布"
            time = "待公布"
            
            # 尋找活動日期
            date_patterns = [
                r'活動日期[：:\s]*(\d{4}/\d{1,2}/\d{1,2})',
                r'(\d{4}/\d{1,2}/\d{1,2})\([日一二三四五六]\)',
                r'(\d{4}-\d{2}-\d{2})',
            ]
            
            page_text = soup.get_text()
            for pattern in date_patterns:
                match = re.search(pattern, page_text)
                if match:
                    date = match.group(1)
                    break
            
            # 尋找時間 - 改進時間解析
            time_patterns = [
                r'(\d{1,2}:\d{2})',
                r'(\d{1,2}點\d{2}分)',
                r'時間[：:\s]*(\d{1,2}:\d{2})',
                r'開演[：:\s]*(\d{1,2}:\d{2})',
            ]

            for pattern in time_patterns:
                match = re.search(pattern, page_text)
                if match:
                    raw_time = match.group(1)
                    # 確保時間格式正確，避免 "01:20" 這種錯誤
                    if ':' in raw_time:
                        hour, minute = raw_time.split(':')
                        hour = int(hour)
                        minute = int(minute)
                        # 如果小時數看起來不合理（如01點），可能是解析錯誤
                        if 6 <= hour <= 23:  # 合理的演出時間範圍
                            time = f"{hour:02d}:{minute:02d}"
                            break
                    else:
                        time = raw_time
                        break

            # 提取場地信息 - 改進解析，避免包含地址等無關信息
            venue = "待公布"
            venue_patterns = [
                # 優先匹配簡潔的場地名稱
                r'([^。\n]{3,25}(?:巨蛋|體育館|展覽館|音樂廳|劇院|會館|中心|廳))(?![^。\n]*(?:地址|號|市|區|路|街))',
                r'活動地點[：:\s]*([^。\n]{5,25}(?:巨蛋|體育館|展覽館|音樂廳|劇院|會館|中心|廳))',
                r'場地[：:\s]*([^。\n]{3,25}(?:巨蛋|體育館|展覽館|音樂廳|劇院|會館|中心|廳))',
            ]

            for pattern in venue_patterns:
                match = re.search(pattern, page_text)
                if match:
                    potential_venue = match.group(1).strip()
                    # 過濾掉包含地址信息的結果
                    if not any(keyword in potential_venue for keyword in ['地址', '號', '市民大道', '捷運', '站', '路', '街', '巷']):
                        venue = potential_venue
                        break

            # 如果仍然沒找到，嘗試更簡單的模式
            if venue == "待公布":
                simple_venue_patterns = [
                    r'(臺北流行音樂中心)',
                    r'(高雄巨蛋)',
                    r'(小巨蛋)',
                    r'(國父紀念館)',
                ]
                for pattern in simple_venue_patterns:
                    if re.search(pattern, page_text):
                        venue = re.search(pattern, page_text).group(1)
                        break

            return EventInfo(
                title=title,
                date=date,
                time=time,
                venue=venue,
                url=event_url,
                event_id=event_id
            )

        except Exception as e:
            logger.error(f"Error parsing event info from {event_url}: {e}")
            return EventInfo(
                title=f"KHAM Event {event_id}",
                date="待公布",
                time="待公布",
                venue="待公布",
                url=event_url,
                event_id=event_id
            )

    async def _get_ticket_areas(self, event_url: str, event_id: str) -> List[TicketArea]:
        """
        獲取票券區域信息

        Args:
            event_url: 活動URL
            event_id: 活動ID

        Returns:
            List[TicketArea]: 票券區域列表
        """
        try:
            html_content = await self._fetch_page(event_url)
            if not html_content:
                logger.warning(f"Failed to fetch ticket page: {event_url}")
                return []

            soup = BeautifulSoup(html_content, 'lxml')
            ticket_areas = []

            # 寬宏的票券信息通常在表格中
            # 尋找票券表格
            table_selectors = [
                'table', '.ticket-table', '.area-table'
            ]

            ticket_table = None
            for selector in table_selectors:
                table = soup.select_one(selector)
                if table:
                    # 檢查表格是否包含票券信息
                    table_text = table.get_text()
                    if any(keyword in table_text for keyword in ['票區', '票價', '空位', '已售完', '熱賣中']):
                        ticket_table = table
                        break

            if not ticket_table:
                # 如果沒找到表格，嘗試從整個頁面解析
                return self._parse_ticket_areas_from_page(soup)

            # 解析表格中的票券信息
            rows = ticket_table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 3:  # 至少需要票區、票價、狀態三列
                    area = self._parse_ticket_row(cells)
                    if area:
                        ticket_areas.append(area)

            # 去重和排序 - 改進去重邏輯
            unique_areas = {}
            for area in ticket_areas:
                # 使用區域名稱和價格作為唯一鍵
                key = f"{area.name}_{area.price}"
                if key not in unique_areas:
                    unique_areas[key] = area
                else:
                    # 如果已存在，保留票數較多的
                    existing = unique_areas[key]
                    if area.available_count > existing.available_count:
                        unique_areas[key] = area

            result = list(unique_areas.values())
            # 按價格降序排列（高價票在前）
            result.sort(key=lambda x: (-x.price, x.name))

            logger.info(f"Found {len(result)} ticket areas for {event_id}")
            return result

        except Exception as e:
            logger.error(f"Error parsing ticket areas from {event_url}: {e}")
            return []

    def _parse_ticket_row(self, cells) -> Optional[TicketArea]:
        """
        解析表格行中的票券信息

        根據實際寬宏網站格式：
        顏色 | 票區 | 票價(NT$) | 空位
        例如：1樓特B區6680元 | 6,680 | 66

        Args:
            cells: 表格單元格列表

        Returns:
            TicketArea: 解析出的票券區域，失敗時返回None
        """
        try:
            if len(cells) < 3:
                return None

            # 根據實際表格結構解析
            # 第1列：顏色（忽略）
            # 第2列：票區（如：1樓特B區6680元）
            # 第3列：票價（如：6,680）
            # 第4列：空位（如：66、已售完、熱賣中）

            area_name = ""
            price = 0
            available_count = 0
            status = TicketStatus.UNKNOWN

            # 解析票區信息（第2列）
            if len(cells) >= 2:
                area_cell = cells[1].get_text(strip=True)
                # 從 "1樓特B區6680元" 中提取區域名稱
                area_match = re.search(r'([^0-9]*\d*樓[^0-9]*[A-Z]區)', area_cell)
                if area_match:
                    area_name = area_match.group(1)
                else:
                    # 備用解析：直接取到"元"之前的部分
                    area_match = re.search(r'(.+?)(?:\d+元|$)', area_cell)
                    if area_match:
                        area_name = area_match.group(1).strip()

            # 解析票價（第3列）
            if len(cells) >= 3:
                price_cell = cells[2].get_text(strip=True)
                price_match = re.search(r'(\d{1,2},?\d{3,4})', price_cell)
                if price_match:
                    price = int(price_match.group(1).replace(',', ''))

            # 解析空位狀態（第4列）
            if len(cells) >= 4:
                status_cell = cells[3].get_text(strip=True)

                if '已售完' in status_cell:
                    status = TicketStatus.SOLD_OUT
                    available_count = 0
                elif '熱賣中' in status_cell:
                    status = TicketStatus.AVAILABLE
                    available_count = 1  # 熱賣中表示有票但數量不明
                else:
                    # 嘗試提取數字
                    number_match = re.search(r'(\d+)', status_cell)
                    if number_match:
                        available_count = int(number_match.group(1))
                        status = TicketStatus.AVAILABLE

            # 驗證解析結果
            if not area_name or not price:
                return None

            # 只返回有票的區域
            if status != TicketStatus.AVAILABLE or available_count <= 0:
                return None

            return TicketArea(
                name=area_name,
                price=price,
                status=status,
                available_count=available_count
            )

        except Exception as e:
            logger.debug(f"Error parsing ticket row: {e}")
            return None

    def _parse_ticket_areas_from_page(self, soup) -> List[TicketArea]:
        """
        從整個頁面解析票券信息（備用方法）

        根據實際寬宏數據格式解析：
        1樓特B區6680元 6,680 66
        1樓特A區5680元 5,680 50
        2樓2B區4880元 4,880 熱賣中
        2樓2A區4880元 4,880 已售完

        Args:
            soup: BeautifulSoup 對象

        Returns:
            List[TicketArea]: 票券區域列表
        """
        ticket_areas = []
        page_text = soup.get_text()

        # 改進的正則表達式，匹配實際格式
        patterns = [
            # 格式：1樓特B區6680元 6,680 66
            r'(\d+樓[^0-9]+[A-Z]區)\d+元\s+[\d,]+\s+(\d+)',
            # 格式：1樓特B區6680元 6,680 熱賣中
            r'(\d+樓[^0-9]+[A-Z]區)\d+元\s+[\d,]+\s+熱賣中',
            # 更通用的格式
            r'([^0-9]*\d+樓[^0-9]*[A-Z]區)(\d+)元\s+([\d,]+)\s+(\d+|熱賣中|已售完)',
        ]

        # 首先嘗試從頁面文本中找到所有票券信息
        lines = page_text.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 檢查是否包含票區信息，但排除HTML標籤和無關內容
            if ('樓' in line and '區' in line and '元' in line and
                not any(keyword in line for keyword in ['<', '>', 'script', 'style', '滑鼠', '點選', '放大', '縮小', '舞台', '座位圖'])):
                area = self._parse_ticket_line(line)
                if area and area.status == TicketStatus.AVAILABLE and area.available_count > 0:
                    ticket_areas.append(area)

        # 如果沒有找到，使用正則表達式
        if not ticket_areas:
            for pattern in patterns:
                matches = re.finditer(pattern, page_text)
                for match in matches:
                    try:
                        area_name = match.group(1).strip()

                        # 提取價格
                        price_match = re.search(r'(\d+)元', match.group(0))
                        if not price_match:
                            continue
                        price = int(price_match.group(1))

                        # 提取狀態和數量
                        if len(match.groups()) >= 4:
                            status_text = match.group(4)
                        else:
                            status_text = match.group(0).split()[-1]

                        if '已售完' in status_text:
                            continue  # 跳過售完的
                        elif '熱賣中' in status_text:
                            available_count = 1
                            status = TicketStatus.AVAILABLE
                        elif status_text.isdigit():
                            available_count = int(status_text)
                            status = TicketStatus.AVAILABLE
                        else:
                            continue

                        ticket_areas.append(TicketArea(
                            name=area_name,
                            price=price,
                            status=status,
                            available_count=available_count
                        ))

                    except Exception as e:
                        logger.debug(f"Error parsing match {match.group(0)}: {e}")
                        continue

        return ticket_areas

    def _parse_ticket_line(self, line: str) -> Optional[TicketArea]:
        """
        解析單行票券信息

        Args:
            line: 票券信息行，如 "1樓特B區6680元 6,680 66"

        Returns:
            TicketArea: 解析出的票券區域，失敗時返回None
        """
        try:
            # 分割行內容
            parts = line.split()
            if len(parts) < 3:
                return None

            # 第一部分：票區名稱和價格（如：1樓特B區6680元）
            area_price_part = parts[0]

            # 提取區域名稱 - 改進正則表達式以支持 "2樓2A區" 格式
            area_match = re.search(r'(\d+樓\d*[A-Z]*區)', area_price_part)
            if not area_match:
                # 備用模式：支持 "1樓特B區" 格式
                area_match = re.search(r'(\d+樓[^0-9]*[A-Z]區)', area_price_part)
            if not area_match:
                return None
            area_name = area_match.group(1)

            # 提取價格
            price_match = re.search(r'(\d+)元', area_price_part)
            if not price_match:
                return None
            price = int(price_match.group(1))

            # 第三部分：狀態（如：66、熱賣中、已售完）
            status_part = parts[2] if len(parts) > 2 else ""

            # 檢查整行是否包含狀態信息
            full_line = " ".join(parts)

            if '已售完' in full_line:
                return None  # 不返回售完的區域
            elif '熱賣中' in full_line:
                return TicketArea(
                    name=area_name,
                    price=price,
                    status=TicketStatus.AVAILABLE,
                    available_count=1
                )
            elif status_part.isdigit():
                available_count = int(status_part)
                if available_count > 0:
                    return TicketArea(
                        name=area_name,
                        price=price,
                        status=TicketStatus.AVAILABLE,
                        available_count=available_count
                    )

            return None

        except Exception as e:
            logger.debug(f"Error parsing ticket line '{line}': {e}")
            return None

    async def test_connection(self) -> bool:
        """
        測試與寬宏的連接

        Returns:
            bool: 連接是否正常
        """
        try:
            self.logger.info("Testing connection to KHAM...")

            # 測試訪問首頁
            test_url = "https://kham.com.tw/"
            html_content = await self._fetch_page(test_url)

            if html_content and "kham" in html_content.lower():
                self.logger.info("KHAM connection test successful")
                return True
            else:
                self.logger.warning("KHAM connection test failed - unexpected response")
                return False

        except Exception as e:
            self.logger.error(f"KHAM connection test failed: {e}")
            return False
        finally:
            # 清理 session
            await self._close_session()

    async def __aenter__(self):
        """異步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        await self._close_session()
