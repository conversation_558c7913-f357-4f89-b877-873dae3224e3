"""
寬宏售票 (KHAM) 爬蟲實現
"""

import re
import asyncio
import logging
import aiohttp
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin, parse_qs
from bs4 import BeautifulSoup

from monitors.ticket_crawler.sites.base import (
    BaseCrawler, CrawlResult, EventInfo, TicketArea, TicketStatus
)
from monitors.ticket_crawler.utils.anti_detection import AntiDetectionMixin
from monitors.ticket_crawler.utils.parsers import DataParser

logger = logging.getLogger('monitors.ticket_crawler.kham')

class KhamCrawler(BaseCrawler, AntiDetectionMixin):
    """
    寬宏售票網站爬蟲
    支持監控 kham.com.tw 的票券可用性
    """
    
    def __init__(self, **kwargs):
        BaseCrawler.__init__(
            self,
            site_name="KHAM",
            base_url="https://kham.com.tw",
            **kwargs
        )
        AntiDetectionMixin.__init__(self)

        # 寬宏特定配置
        self.ticket_base_url = "https://kham.com.tw/application/UTK02/UTK0204_.aspx"

        # HTTP session
        self.session = None
        
    def extract_event_id(self, event_url: str) -> Optional[str]:
        """
        從寬宏URL中提取活動ID
        
        支持的URL格式：
        - https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK&PRODUCT_ID=P0T9WGF5
        
        Args:
            event_url: 活動URL
            
        Returns:
            str: 活動ID組合（如 "P0TEI9AK_P0T9WGF5"）
        """
        try:
            parsed = urlparse(event_url)
            query_params = parse_qs(parsed.query)
            
            performance_id = query_params.get('PERFORMANCE_ID', [None])[0]
            product_id = query_params.get('PRODUCT_ID', [None])[0]
            
            if performance_id and product_id:
                return f"{performance_id}_{product_id}"
            elif performance_id:
                return performance_id
                
        except Exception as e:
            logger.debug(f"Error extracting event ID from {event_url}: {e}")
        
        return None
    
    def is_valid_url(self, url: str) -> bool:
        """
        檢查是否為有效的寬宏URL
        
        Args:
            url: 要檢查的URL
            
        Returns:
            bool: 是否有效
        """
        if not url:
            return False
            
        parsed = urlparse(url)
        if parsed.netloc != 'kham.com.tw':
            return False
            
        # 檢查路徑格式
        if '/UTK0204_.aspx' not in parsed.path:
            return False
            
        # 檢查必要參數
        query_params = parse_qs(parsed.query)
        return 'PERFORMANCE_ID' in query_params
    
    async def crawl_event(self, event_url: str) -> CrawlResult:
        """
        爬取寬宏活動的票券信息
        
        Args:
            event_url: 活動URL
            
        Returns:
            CrawlResult: 爬取結果
        """
        try:
            # 驗證URL
            if not self.is_valid_url(event_url):
                return CrawlResult(
                    success=False,
                    error_message=f"Invalid KHAM URL: {event_url}"
                )
            
            # 提取活動ID
            event_id = self.extract_event_id(event_url)
            if not event_id:
                return CrawlResult(
                    success=False,
                    error_message=f"Cannot extract event ID from URL: {event_url}"
                )
            
            self.logger.info(f"Starting to crawl KHAM event: {event_id}")
            
            # 首先嘗試獲取活動基本信息
            event_info = await self._get_event_info(event_url, event_id)
            
            # 然後獲取票券信息
            ticket_areas = await self._get_ticket_areas(event_url, event_id)
            
            return CrawlResult(
                success=True,
                event_info=event_info,
                ticket_areas=ticket_areas
            )
            
        except Exception as e:
            self.logger.error(f"Error crawling KHAM event {event_url}: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e)
            )
    
    async def _get_session(self):
        """獲取或創建 HTTP session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def _close_session(self):
        """關閉 HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _fetch_page(self, url: str) -> Optional[str]:
        """
        獲取網頁內容

        Args:
            url: 要獲取的URL

        Returns:
            str: 網頁HTML內容，失敗時返回None
        """
        session = await self._get_session()

        # 減少反爬蟲延遲以提高速度
        await self.random_delay(min_seconds=0.8, max_seconds=2.0)

        # 獲取隨機化的請求頭
        headers = self.get_random_headers({
            'Referer': 'https://kham.com.tw/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        })

        for attempt in range(self.max_retries):
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.debug(f"Successfully fetched {url}")
                        return content
                    elif response.status == 429:
                        # 被限流，等待較短時間
                        wait_time = min(2 ** attempt, 8)  # 最多等待8秒
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")

            except asyncio.TimeoutError:
                logger.warning(f"Timeout on attempt {attempt + 1} for {url}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))
            except Exception as e:
                logger.error(f"Error fetching {url} on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))

        return None

    async def _get_event_info(self, event_url: str, event_id: str) -> EventInfo:
        """
        獲取活動基本信息

        Args:
            event_url: 活動URL
            event_id: 活動ID

        Returns:
            EventInfo: 活動信息
        """
        try:
            html_content = await self._fetch_page(event_url)
            if not html_content:
                logger.warning(f"Failed to fetch event page: {event_url}")
                return EventInfo(
                    title=f"KHAM Event {event_id}",
                    date="待公布",
                    time="待公布",
                    venue="待公布",
                    url=event_url,
                    event_id=event_id
                )

            soup = BeautifulSoup(html_content, 'lxml')

            # 提取活動標題
            title = "Unknown Event"
            title_selectors = [
                'h1', 'h2', '.event-title', '.activity-title',
                '.title', '.main-title'
            ]
            
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    raw_title = title_elem.get_text(strip=True)
                    if raw_title and len(raw_title) > 5:
                        title = raw_title.strip()
                        break
            
            # 如果沒找到標題，從頁面文本中提取
            if title == "Unknown Event":
                page_text = soup.get_text()
                # 尋找演唱會、音樂會等關鍵字附近的文本
                concert_patterns = [
                    r'([^。\n]{5,80}(?:演唱會|音樂會|演出|表演|活動|展覽))',
                    r'([\w\s]{5,80}(?:Concert|Live|Show|Exhibition))',
                ]
                for pattern in concert_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        title = match.group(1).strip()
                        break

            # 提取日期時間信息
            date = "待公布"
            time = "待公布"
            
            # 尋找活動日期
            date_patterns = [
                r'活動日期[：:\s]*(\d{4}/\d{1,2}/\d{1,2})',
                r'(\d{4}/\d{1,2}/\d{1,2})\([日一二三四五六]\)',
                r'(\d{4}-\d{2}-\d{2})',
            ]
            
            page_text = soup.get_text()
            for pattern in date_patterns:
                match = re.search(pattern, page_text)
                if match:
                    date = match.group(1)
                    break
            
            # 尋找時間
            time_patterns = [
                r'(\d{1,2}:\d{2})',
                r'(\d{1,2}點\d{2}分)',
            ]
            
            for pattern in time_patterns:
                match = re.search(pattern, page_text)
                if match:
                    time = match.group(1)
                    break

            # 提取場地信息
            venue = "待公布"
            venue_patterns = [
                r'活動地點[：:\s]*([^。\n]{5,50})',
                r'([^。\n]{3,30}(?:巨蛋|體育館|展覽館|音樂廳|劇院|會館|中心|廳))',
            ]
            
            for pattern in venue_patterns:
                match = re.search(pattern, page_text)
                if match:
                    venue = match.group(1).strip()
                    break

            return EventInfo(
                title=title,
                date=date,
                time=time,
                venue=venue,
                url=event_url,
                event_id=event_id
            )

        except Exception as e:
            logger.error(f"Error parsing event info from {event_url}: {e}")
            return EventInfo(
                title=f"KHAM Event {event_id}",
                date="待公布",
                time="待公布",
                venue="待公布",
                url=event_url,
                event_id=event_id
            )

    async def _get_ticket_areas(self, event_url: str, event_id: str) -> List[TicketArea]:
        """
        獲取票券區域信息

        Args:
            event_url: 活動URL
            event_id: 活動ID

        Returns:
            List[TicketArea]: 票券區域列表
        """
        try:
            html_content = await self._fetch_page(event_url)
            if not html_content:
                logger.warning(f"Failed to fetch ticket page: {event_url}")
                return []

            soup = BeautifulSoup(html_content, 'lxml')
            ticket_areas = []

            # 寬宏的票券信息通常在表格中
            # 尋找票券表格
            table_selectors = [
                'table', '.ticket-table', '.area-table'
            ]

            ticket_table = None
            for selector in table_selectors:
                table = soup.select_one(selector)
                if table:
                    # 檢查表格是否包含票券信息
                    table_text = table.get_text()
                    if any(keyword in table_text for keyword in ['票區', '票價', '空位', '已售完', '熱賣中']):
                        ticket_table = table
                        break

            if not ticket_table:
                # 如果沒找到表格，嘗試從整個頁面解析
                return self._parse_ticket_areas_from_page(soup)

            # 解析表格中的票券信息
            rows = ticket_table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 3:  # 至少需要票區、票價、狀態三列
                    area = self._parse_ticket_row(cells)
                    if area:
                        ticket_areas.append(area)

            # 去重和排序
            unique_areas = {}
            for area in ticket_areas:
                key = f"{area.name}_{area.price}"
                if key not in unique_areas:
                    unique_areas[key] = area

            result = list(unique_areas.values())
            result.sort(key=lambda x: (x.price, x.name))

            logger.info(f"Found {len(result)} ticket areas for {event_id}")
            return result

        except Exception as e:
            logger.error(f"Error parsing ticket areas from {event_url}: {e}")
            return []

    def _parse_ticket_row(self, cells) -> Optional[TicketArea]:
        """
        解析表格行中的票券信息

        Args:
            cells: 表格單元格列表

        Returns:
            TicketArea: 解析出的票券區域，失敗時返回None
        """
        try:
            if len(cells) < 3:
                return None

            # 提取票區名稱（通常在第一列或第二列）
            area_name = ""
            price = 0
            status_text = ""
            available_count = 0

            for cell in cells:
                cell_text = cell.get_text(strip=True)

                # 檢查是否為票區名稱
                if any(keyword in cell_text for keyword in ['區', 'A區', 'B區', 'C區', '樓']):
                    if not area_name:  # 只取第一個匹配的
                        area_name = cell_text

                # 檢查是否為價格
                price_match = re.search(r'(\d{1,2},?\d{3,4})', cell_text)
                if price_match and not price:
                    price = int(price_match.group(1).replace(',', ''))

                # 檢查狀態
                if any(keyword in cell_text for keyword in ['已售完', '售完', '熱賣中', '空位']):
                    status_text = cell_text

                    # 提取數字（空位數）
                    number_match = re.search(r'(\d+)', cell_text)
                    if number_match:
                        available_count = int(number_match.group(1))

            if not area_name or not price:
                return None

            # 判斷狀態
            if '已售完' in status_text or '售完' in status_text:
                status = TicketStatus.SOLD_OUT
                available_count = 0
            elif '熱賣中' in status_text:
                status = TicketStatus.AVAILABLE
                available_count = available_count or 1  # 熱賣中表示有票但數量不明
            elif available_count > 0:
                status = TicketStatus.AVAILABLE
            else:
                status = TicketStatus.UNKNOWN

            return TicketArea(
                name=area_name,
                price=price,
                status=status,
                available_count=available_count
            )

        except Exception as e:
            logger.debug(f"Error parsing ticket row: {e}")
            return None

    def _parse_ticket_areas_from_page(self, soup) -> List[TicketArea]:
        """
        從整個頁面解析票券信息（備用方法）

        Args:
            soup: BeautifulSoup 對象

        Returns:
            List[TicketArea]: 票券區域列表
        """
        ticket_areas = []
        page_text = soup.get_text()

        # 使用正則表達式匹配票券信息
        # 格式如：1樓特B區6680元 66
        patterns = [
            r'(\d+樓[^0-9]+區)\s*(\d{1,2},?\d{3,4})元\s*(\d+)',
            r'([^0-9]+區)\s*(\d{1,2},?\d{3,4})元\s*(\d+)',
            r'([^0-9]+區)\s*(\d{1,2},?\d{3,4})\s*已售完',
            r'([^0-9]+區)\s*(\d{1,2},?\d{3,4})\s*熱賣中',
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, page_text)
            for match in matches:
                try:
                    area_name = match.group(1).strip()
                    price = int(match.group(2).replace(',', ''))

                    if len(match.groups()) >= 3:
                        third_group = match.group(3)
                        if third_group.isdigit():
                            available_count = int(third_group)
                            status = TicketStatus.AVAILABLE
                        elif '已售完' in third_group:
                            available_count = 0
                            status = TicketStatus.SOLD_OUT
                        elif '熱賣中' in third_group:
                            available_count = 1
                            status = TicketStatus.AVAILABLE
                        else:
                            continue
                    else:
                        # 檢查是否為售完
                        if '已售完' in match.group(0):
                            available_count = 0
                            status = TicketStatus.SOLD_OUT
                        elif '熱賣中' in match.group(0):
                            available_count = 1
                            status = TicketStatus.AVAILABLE
                        else:
                            continue

                    ticket_areas.append(TicketArea(
                        name=area_name,
                        price=price,
                        status=status,
                        available_count=available_count
                    ))

                except Exception as e:
                    logger.debug(f"Error parsing match {match.group(0)}: {e}")
                    continue

        return ticket_areas

    async def test_connection(self) -> bool:
        """
        測試與寬宏的連接

        Returns:
            bool: 連接是否正常
        """
        try:
            self.logger.info("Testing connection to KHAM...")

            # 測試訪問首頁
            test_url = "https://kham.com.tw/"
            html_content = await self._fetch_page(test_url)

            if html_content and "kham" in html_content.lower():
                self.logger.info("KHAM connection test successful")
                return True
            else:
                self.logger.warning("KHAM connection test failed - unexpected response")
                return False

        except Exception as e:
            self.logger.error(f"KHAM connection test failed: {e}")
            return False
        finally:
            # 清理 session
            await self._close_session()

    async def __aenter__(self):
        """異步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        await self._close_session()
