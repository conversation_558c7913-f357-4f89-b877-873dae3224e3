# 改進功能說明

## 🎯 已完成的改進

### 1. ✅ 每個網站獨立的Discord頻道

**問題**：之前所有爬蟲通知都發送到同一個頻道，難以區分不同網站的消息。

**解決方案**：
```json
{
  "crawler_channels": {
    "tixcraft": 1375358031428321301,  // 拓元售票專用頻道
    "kham": 1381549590075019274       // 寬宏售票專用頻道
  }
}
```

**頻道選擇邏輯**：
1. 優先使用網站專用頻道 (`crawler_channels.{site_name}`)
2. 回退到通用爬蟲頻道 (`crawler_channel_id`)
3. 最後回退到主頻道 (`discord_channel_id`)

### 2. ✅ 顯示所有有票區域

**問題**：之前限制只顯示15個有票區域，會省略很多信息。

**解決方案**：
- 移除數量限制，顯示所有有票區域
- 智能處理Discord字符限制（1024字符/欄位）
- 自動分割成多個欄位如果內容太長

**效果**：
```
✅ 有票區域
• 1樓特B區 ($6680) - 66張
• 1樓特A區 ($5680) - 50張
• 1樓特C區 ($5680) - 37張
... (顯示所有40個有票區域)
```

### 3. ✅ 高頻通知策略

**問題**：之前只在票數增加時通知，錯過很多重要變化。

**新的通知觸發條件**：
- ✅ 首次運行
- ✅ 從無票到有票
- ✅ 票數增加
- ✅ **票數減少但仍有票** (新增)
- ✅ **從有票到無票** (新增)

**智能冷卻時間**：
- 從無票到有票：**立即通知**（0秒冷卻）
- 票數變化：**30秒冷卻**
- 其他情況：3600秒冷卻

### 4. ✅ 詳細的狀態反饋

**新的頁腳信息**：
- 🕷️ 爬蟲啟動通知 - 目前票券狀態
- 🎉 票券現已開放！(爬蟲檢測)
- 📈 票券數量增加！(爬蟲檢測)
- 📉 **票券數量減少！(爬蟲檢測)** (新增)
- ❌ **票券已售完！(爬蟲檢測)** (新增)
- 🔄 票券狀態更新 (爬蟲檢測)

## 📊 通知頻率對比

### 之前的通知策略
```
只在以下情況通知：
- 首次運行
- 從無票到有票
- 票數增加

冷卻時間：3600秒（1小時）
```

### 現在的通知策略
```
在以下情況通知：
- 首次運行
- 從無票到有票 (立即通知)
- 票數增加 (30秒冷卻)
- 票數減少 (30秒冷卻) ← 新增
- 從有票到無票 (30秒冷卻) ← 新增

實現了類似其他監控機器人的高頻通知！
```

## 🔧 配置文件更新

### 新增配置項
```json
{
  "crawler_channels": {
    "tixcraft": 1375358031428321301,
    "kham": 1381549590075019274
  },
  "crawler_notification_cooldown": 30
}
```

### 完整配置示例
```json
{
  "discord_token": "your_token",
  "discord_channel_id": 1375358031428321301,
  "crawler_channels": {
    "tixcraft": 1375358031428321301,
    "kham": 1381549590075019274
  },
  "crawler_notification_cooldown": 30,
  "enable_cooldown": true,
  "ticket_crawler": {
    "enabled": true,
    "check_interval": 180,
    "sites": {
      "tixcraft": {
        "enabled": true,
        "urls": ["https://tixcraft.com/ticket/area/25_xalive/19055"],
        "max_retries": 2,
        "request_delay": 1.0
      },
      "kham": {
        "enabled": true,
        "urls": ["https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK&PRODUCT_ID=P0T9WGF5"],
        "max_retries": 2,
        "request_delay": 1.2
      }
    }
  }
}
```

## 🚀 實際效果

### 高頻通知示例
```
17:00:00 - 📈 票券數量增加！(100張 → 120張)
17:00:35 - 📉 票券數量減少！(120張 → 80張)
17:01:10 - 📉 票券數量減少！(80張 → 50張)
17:01:45 - 📈 票券數量增加！(50張 → 70張)
17:02:20 - ❌ 票券已售完！(70張 → 0張)
17:02:55 - 🎉 票券現已開放！(0張 → 30張)
```

### 頻道分離效果
```
#tixcraft-tickets 頻道：
🎫 蕭秉治演唱會
網站: TIXCRAFT
• 橙 ($207) - 16張
• 紅 ($218) - 18張

#kham-tickets 頻道：
🎫 莎拉布萊曼演唱會
網站: KHAM
• 1樓特B區 ($6680) - 66張
• 1樓特A區 ($5680) - 50張
```

## 📈 性能影響

### 通知頻率增加
- 之前：平均每小時1-2次通知
- 現在：票券變化時每30秒可能通知一次
- 高峰期：可能每分鐘多次通知

### 資源使用
- Discord API調用增加
- 但爬蟲頻率未變（仍為180秒）
- 整體影響可控

## ⚠️ 注意事項

### Discord頻道設置
1. 確保機器人有權限發送消息到指定頻道
2. 建議為不同網站創建專用頻道
3. 可以設置頻道權限控制誰能看到通知

### 通知頻率管理
1. 可以通過 `crawler_notification_cooldown` 調整冷卻時間
2. 可以通過 `enable_cooldown` 完全關閉冷卻
3. 建議保持30秒冷卻以避免過度通知

### 測試建議
1. 先在測試頻道測試配置
2. 觀察通知頻率是否符合預期
3. 根據需要調整冷卻時間

## 🎉 總結

現在您的票券監控機器人具備了：

✅ **多網站獨立頻道** - 清晰區分不同票務系統
✅ **完整票券信息** - 顯示所有有票區域
✅ **高頻通知** - 類似專業監控機器人的響應速度
✅ **智能冷卻** - 重要變化立即通知，避免垃圾信息

這些改進讓您的機器人達到了專業票券監控機器人的水準！
