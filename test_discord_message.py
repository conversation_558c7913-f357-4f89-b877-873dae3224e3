#!/usr/bin/env python3
"""
測試 Discord 消息格式化
"""

import sys
import os
import datetime

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from monitors.ticket_crawler.sites.base import EventInfo, TicketArea, TicketStatus, CrawlResult
from monitors.ticket_crawler.monitor import TicketCrawlerMonitor
from ticket_bot.utils import taiwan_tz

def create_test_data():
    """創建測試數據"""
    # 創建活動信息
    event_info = EventInfo(
        title="蕭秉治Xiao Bing Chi<PERSON>《活著Alive》高雄巨蛋演唱會",
        date="2024-03-15",
        time="19:30",
        venue="高雄巨蛋",
        url="https://tixcraft.com/ticket/area/25_xalive/19055",
        event_id="25_xalive"
    )
    
    # 創建票券區域（只有有票的）
    ticket_areas = [
        TicketArea(name="橙", price=207, status=TicketStatus.AVAILABLE, available_count=16),
        TicketArea(name="橙", price=208, status=TicketStatus.AVAILABLE, available_count=4),
        TicketArea(name="紅", price=216, status=TicketStatus.AVAILABLE, available_count=4),
        TicketArea(name="紅", price=218, status=TicketStatus.AVAILABLE, available_count=18),
        TicketArea(name="紅", price=219, status=TicketStatus.AVAILABLE, available_count=58),
        TicketArea(name="紅", price=220, status=TicketStatus.AVAILABLE, available_count=58),
        TicketArea(name="狂歡人生站區", price=2800, status=TicketStatus.AVAILABLE, available_count=18),
    ]
    
    return CrawlResult(
        success=True,
        event_info=event_info,
        ticket_areas=ticket_areas
    )

def test_discord_message_format():
    """測試 Discord 消息格式化"""
    print("測試 Discord 消息格式化")
    print("=" * 50)
    
    # 創建測試數據
    result = create_test_data()
    
    # 創建一個模擬的監控器實例
    class MockBot:
        pass
    
    monitor = TicketCrawlerMonitor(MockBot())
    
    # 測試消息格式化
    embed = monitor._create_crawler_notification_embed(
        result=result,
        site_name="tixcraft",
        first_run=True,
        previous_available=0
    )
    
    print(f"標題: {embed.title}")
    print(f"描述: {embed.description}")
    print(f"顏色: {embed.color}")
    
    print("\n欄位:")
    for field in embed.fields:
        print(f"  {field.name}:")
        for line in field.value.split('\n'):
            print(f"    {line}")
    
    print(f"\n頁腳: {embed.footer.text}")
    print(f"時間戳: {embed.timestamp}")
    
    print("\n" + "=" * 50)
    print("預期結果:")
    print("✅ 不應該顯示 TBD")
    print("✅ 不應該顯示售完區域")
    print("✅ 不應該顯示總票數統計")
    print("✅ 票數應該正確解析（16, 4, 4, 18, 58, 58, 18）")
    print("✅ 價格應該正確顯示（207, 208, 216, 218, 219, 220, 2800）")

def test_tbd_handling():
    """測試 TBD 處理"""
    print("\n\n測試 TBD 處理")
    print("=" * 50)
    
    # 創建包含 TBD 的測試數據
    event_info_with_tbd = EventInfo(
        title="測試活動",
        date="TBD",
        time="TBD", 
        venue="TBD",
        url="https://example.com",
        event_id="test"
    )
    
    result_with_tbd = CrawlResult(
        success=True,
        event_info=event_info_with_tbd,
        ticket_areas=[]
    )
    
    class MockBot:
        pass
    
    monitor = TicketCrawlerMonitor(MockBot())
    
    embed = monitor._create_crawler_notification_embed(
        result=result_with_tbd,
        site_name="tixcraft",
        first_run=True,
        previous_available=0
    )
    
    print(f"標題: {embed.title}")
    print(f"描述: {embed.description}")
    
    # 檢查是否包含 TBD
    has_tbd = "TBD" in embed.description
    print(f"\n包含 TBD: {'❌ 是' if has_tbd else '✅ 否'}")

if __name__ == "__main__":
    test_discord_message_format()
    test_tbd_handling()
