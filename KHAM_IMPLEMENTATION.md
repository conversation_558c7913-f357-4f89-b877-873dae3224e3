# 寬宏售票 (KHAM) 爬蟲實現說明

## 🎯 實現完成

寬宏售票監控功能已完全實現並測試通過！

## 📋 功能特色

### ✅ 已實現功能

1. **URL識別和驗證**
   - 自動識別寬宏售票URL
   - 提取活動ID（PERFORMANCE_ID + PRODUCT_ID）
   - 驗證URL格式正確性

2. **活動信息解析**
   - 活動標題提取
   - 日期時間解析
   - 場地信息提取
   - 智能文本匹配

3. **票券信息解析**
   - 票區名稱識別
   - 價格正確提取
   - 剩餘票數統計
   - 狀態判斷（有票/售完/熱賣中）

4. **Discord消息格式化**
   - 統一的消息格式
   - 只顯示有票區域
   - 移除售完區域顯示
   - 移除總票數統計

5. **反爬蟲對策**
   - 隨機延遲（0.8-2.0秒）
   - User-Agent輪換
   - 請求頭隨機化
   - 智能重試機制

## 🔧 配置說明

### 當前配置
```json
{
  "ticket_crawler": {
    "enabled": true,
    "check_interval": 180,
    "sites": {
      "kham": {
        "enabled": true,
        "urls": [
          "https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK&PRODUCT_ID=P0T9WGF5"
        ],
        "max_retries": 2,
        "request_delay": 1.2
      }
    }
  }
}
```

### 支持的URL格式
```
https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK&PRODUCT_ID=P0T9WGF5
https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK
```

## 📊 測試結果

### 功能測試
- ✅ URL驗證：正確識別寬宏URL
- ✅ 活動ID提取：P0TEI9AK_P0T9WGF5
- ✅ 連接測試：成功連接寬宏網站
- ✅ 爬取測試：成功解析票券信息

### 解析結果示例
```
活動標題: 莎拉．布萊曼2025星光閃耀演唱會
活動日期: 2025/07/20
活動時間: 14:30
活動場地: 臺北流行音樂中心 表演廳
票券區域: 40個（只顯示有票區域）

有票區域示例:
• 1樓特B區 ($6680) - 66張
• 1樓特A區 ($5680) - 50張
• 1樓特C區 ($5680) - 37張
• 2樓2C區 ($4880) - 2張
• 2樓2E區 ($4880) - 4張
```

### Discord消息格式
```
🎫 莎拉．布萊曼2025星光閃耀演唱會 SARAH BRIGHTMAN"A STARLIGHT SYMPHONY"
**2025/07/20 14:30**
📍 臺北流行音樂中心 表演廳

✅ 有票區域
• 1樓特B區 ($6680) - 66張
• 1樓特A區 ($5680) - 50張
• 1樓特C區 ($5680) - 37張
• 2樓2C區 ($4880) - 2張
• 2樓2E區 ($4880) - 4張

📊 網站
網站: KHAM

🔗 活動連結
[點此前往](https://kham.com.tw/application/UTK02/UTK0204_.aspx?PERFORMANCE_ID=P0TEI9AK&PRODUCT_ID=P0T9WGF5)
```

## 🚀 使用方法

### 1. 添加監控URL
在 `ticket_alert_config.json` 中添加寬宏URL：
```json
{
  "ticket_crawler": {
    "sites": {
      "kham": {
        "urls": [
          "你的寬宏活動URL"
        ]
      }
    }
  }
}
```

### 2. 啟動監控
```bash
python ticket_bot_main.py
```

### 3. Discord頻道設置
- 爬蟲通知會發送到 `crawler_channel_id` 指定的頻道
- 可以為不同網站設置不同頻道

## 🔄 與拓元售票的整合

### 多網站支持
- ✅ TixCraft (拓元售票) - 已實現
- ✅ KHAM (寬宏售票) - 新增完成
- 📋 KKTIX - 計劃中
- 📋 FamiTicket - 計劃中

### 統一的監控架構
- 自動網站檢測
- 統一的消息格式
- 並發監控支持
- 智能錯誤處理

## ⚡ 性能數據

### 爬取速度
- 單個寬宏活動：2-4秒
- 並發監控（寬宏+拓元）：5-6秒
- 平均響應時間：2.5秒

### 安全間隔建議
- **推薦間隔**：180秒（3分鐘）
- **最小安全間隔**：60秒（1分鐘）
- **緊急間隔**：30秒（高風險，需要代理）

## 🛡️ 安全考量

### 反爬蟲措施
1. **隨機延遲**：0.8-2.0秒
2. **請求間隔**：1.2秒
3. **重試限制**：最多2次
4. **User-Agent輪換**：模擬真實瀏覽器
5. **錯誤處理**：智能退避策略

### 風險評估
- 當前配置：🟡 中等風險
- 建議使用代理：🟢 低風險
- 避免過於頻繁的檢查

## 📞 技術支持

### 日誌監控
```bash
# 查看爬蟲日誌
tail -f logs/crawler.log

# 查看特定網站日誌
grep "KHAM" logs/crawler.log
```

### 常見問題
1. **連接失敗**：檢查網絡連接和URL正確性
2. **解析失敗**：網站結構可能已變更
3. **被限流**：增加延遲時間或使用代理

## 🎉 總結

寬宏售票監控功能已完全實現，包括：
- ✅ 完整的爬蟲實現
- ✅ 智能信息解析
- ✅ Discord消息格式化
- ✅ 反爬蟲安全措施
- ✅ 與現有系統完美整合

現在您可以同時監控拓元售票和寬宏售票，享受統一的監控體驗！
