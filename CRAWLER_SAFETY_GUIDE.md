# 爬蟲安全指南

## 檢查間隔建議

### ⚠️ 風險評估

**1秒內檢查間隔的風險：**
- 🚨 **極高風險** - 幾乎必定被封IP
- 🚨 **觸發CDN防護** - Cloudflare等會立即檢測並阻擋
- 🚨 **法律風險** - 可能被視為DDoS攻擊
- 🚨 **服務中斷** - 可能導致整個服務被永久封鎖

### 📊 安全間隔建議

| 間隔時間 | 風險等級 | 適用場景 | 說明 |
|---------|---------|---------|------|
| 1-5秒 | 🚨 極高 | ❌ 不建議 | 幾乎必定被封 |
| 10-30秒 | 🔴 高 | ⚠️ 緊急情況 | 短期使用，需要輪換IP |
| 60-180秒 | 🟡 中等 | ✅ 重要票券 | 平衡效率和安全 |
| 300秒+ | 🟢 低 | ✅ 一般監控 | 最安全的選擇 |

### 🛡️ 反爬蟲對策

**當前實現的安全措施：**
1. **隨機延遲** - 0.5-2.0秒隨機等待
2. **User-Agent輪換** - 模擬不同瀏覽器
3. **請求頭隨機化** - 避免特徵檢測
4. **錯誤重試機制** - 智能退避策略
5. **並發限制** - 避免過多同時請求

## 當前配置

### 現有設置
```json
{
  "ticket_crawler": {
    "check_interval": 180,  // 3分鐘檢查一次
    "anti_detection": {
      "random_delay": [0.5, 1.5],  // 隨機延遲
      "user_agents_rotation": true,
      "proxy_rotation": false
    }
  }
}
```

### 網站特定設置
- **TixCraft**: 1.0秒請求延遲，2次重試
- **KHAM**: 1.2秒請求延遲，2次重試

## 🚀 性能優化建議

### 如果需要更快的監控：

1. **多通道監控**
   ```json
   {
     "crawler_channel_id": 1381549590075019274,  // 寬宏專用
     "tixcraft_channel_id": 1375358031428321301  // 拓元專用
   }
   ```

2. **分散式部署**
   - 使用多個IP地址
   - 不同地區的服務器
   - 輪換代理服務器

3. **智能監控**
   - 開賣前加密監控
   - 平時降低頻率
   - 根據票券狀態調整

### 建議的最佳實踐

**安全的快速監控方案：**
```json
{
  "ticket_crawler": {
    "check_interval": 60,     // 1分鐘（最小安全間隔）
    "sites": {
      "tixcraft": {
        "request_delay": 2.0,   // 增加延遲
        "max_retries": 1        // 減少重試
      },
      "kham": {
        "request_delay": 2.5,   // 更長延遲
        "max_retries": 1
      }
    },
    "anti_detection": {
      "random_delay": [1.0, 3.0],  // 更長隨機延遲
      "user_agents_rotation": true,
      "proxy_rotation": true        // 啟用代理輪換
    }
  }
}
```

## 🔧 實際部署建議

### 生產環境配置

**保守配置（推薦）：**
- 檢查間隔：300秒（5分鐘）
- 請求延遲：2-3秒
- 隨機延遲：1-3秒

**積極配置（需要代理）：**
- 檢查間隔：60秒（1分鐘）
- 請求延遲：1.5-2秒
- 隨機延遲：0.8-2秒
- 必須使用代理輪換

**緊急配置（高風險）：**
- 檢查間隔：30秒
- 請求延遲：3-5秒
- 隨機延遲：2-4秒
- 必須使用多個代理IP
- 建議只在開賣前短期使用

## 📈 監控效果

### 當前實現的監控效果

**寬宏售票監控：**
- ✅ 成功解析活動信息
- ✅ 正確提取票券價格和數量
- ✅ 過濾售完區域
- ✅ Discord消息格式化完美
- ✅ 支持並發監控

**拓元售票監控：**
- ✅ 修正了價格和票數解析
- ✅ 移除了TBD顯示
- ✅ 優化了爬蟲速度
- ✅ 改進了活動信息提取

### 性能數據
- 單個網站爬取：2-4秒
- 並發爬取兩個網站：5-6秒
- 平均每個URL：2.5-3秒

## ⚡ 總結

**回答您的問題：**

1. **檢查間隔可以縮到1秒內嗎？**
   - ❌ **強烈不建議** - 風險極高
   - 最小安全間隔：**60秒**
   - 推薦間隔：**180-300秒**

2. **會有被鎖IP的風險嗎？**
   - 1秒間隔：🚨 **幾乎必定被封**
   - 10-30秒：🔴 **高風險**
   - 60秒以上：🟡 **相對安全**

3. **寬宏監控功能：**
   - ✅ **已完成** - 完整實現
   - ✅ **已測試** - 功能正常
   - ✅ **已集成** - 可立即使用

**建議使用60-180秒的檢查間隔，這樣既能保證及時性，又能避免被封IP的風險。**
