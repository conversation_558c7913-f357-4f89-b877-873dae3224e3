#!/usr/bin/env python3
"""
測試機器人啟動
"""

import asyncio
import logging
import sys
from ticket_bot.config import load_config
from monitors.ticket_api.monitor import TicketAPIMonitor
from monitors.ticket_crawler.monitor import TicketCrawlerMonitor

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_bot_startup')

class MockBot:
    """模擬 Discord 機器人"""
    def __init__(self):
        self.channels = {}
    
    def get_channel(self, channel_id):
        """模擬獲取頻道"""
        logger.info(f"Mock bot getting channel: {channel_id}")
        return MockChannel(channel_id)

class MockChannel:
    """模擬 Discord 頻道"""
    def __init__(self, channel_id):
        self.id = channel_id
    
    async def send(self, content=None, embed=None):
        """模擬發送消息"""
        if embed:
            logger.info(f"Mock channel {self.id} would send embed: {embed.title}")
        else:
            logger.info(f"Mock channel {self.id} would send: {content}")

async def test_monitors():
    """測試監控器"""
    logger.info("=== 測試監控器啟動 ===")
    
    # 加載配置
    config = load_config()
    logger.info(f"配置加載成功")
    logger.info(f"TicketPlus URLs: {len(config.get('urls', []))}")
    logger.info(f"爬蟲啟用: {config.get('ticket_crawler', {}).get('enabled', False)}")
    
    # 創建模擬機器人
    mock_bot = MockBot()
    
    # 測試 API 監控器
    logger.info("1. 測試 TicketPlus API 監控器")
    api_monitor = TicketAPIMonitor(mock_bot)
    
    try:
        # 測試啟動通知
        if config.get("urls"):
            await api_monitor.send_startup_notification()
        else:
            logger.info("沒有 TicketPlus URLs，跳過啟動通知")
        
        # 測試啟動監控
        api_monitor.start_monitoring()
        logger.info("API 監控器啟動成功")
        
        # 停止監控
        api_monitor.stop_monitoring()
        logger.info("API 監控器停止成功")
        
    except Exception as e:
        logger.error(f"API 監控器測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("")
    
    # 測試爬蟲監控器
    logger.info("2. 測試 Tixcraft 爬蟲監控器")
    crawler_monitor = TicketCrawlerMonitor(mock_bot)
    
    try:
        # 測試啟動通知
        if config.get("ticket_crawler", {}).get("enabled", False):
            await crawler_monitor.send_startup_notification()
        else:
            logger.info("爬蟲監控未啟用，跳過啟動通知")
        
        # 測試啟動監控
        crawler_monitor.start_monitoring()
        logger.info("爬蟲監控器啟動成功")
        
        # 獲取統計信息
        stats = crawler_monitor.get_monitor_stats()
        logger.info(f"爬蟲監控統計: {stats}")
        
        # 停止監控
        crawler_monitor.stop_monitoring()
        logger.info("爬蟲監控器停止成功")
        
    except Exception as e:
        logger.error(f"爬蟲監控器測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("=== 測試完成 ===")

def main():
    """主函數"""
    try:
        asyncio.run(test_monitors())
        logger.info("✅ 所有測試完成")
        
    except KeyboardInterrupt:
        logger.info("測試被用戶中斷")
    except Exception as e:
        logger.error(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
