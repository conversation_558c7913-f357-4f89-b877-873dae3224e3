"""
票券爬蟲監控器
管理爬蟲監控任務和 Discord 通知
"""

import time
import asyncio
import logging
import discord
import datetime
from discord.ext import tasks
from typing import Dict, List, Any, Optional

from ticket_bot.config import load_config
from monitors.ticket_crawler.crawler import TicketCrawler
from monitors.ticket_crawler.sites.base import CrawlResult, TicketStatus
from ticket_bot.utils import taiwan_tz

logger = logging.getLogger('monitors.ticket_crawler.monitor')

# 全局變量
previous_crawler_data = {}
last_crawler_notification_time = {}
crawler_notification_cooldown = {}

class TicketCrawlerMonitor:
    """
    票券爬蟲監控器
    管理爬蟲監控任務和通知發送
    """
    
    def __init__(self, bot):
        self.bot = bot
        self.crawler = TicketCrawler()
        self.monitor_task = None
        self.enabled = False
        
    def start_monitoring(self):
        """啟動爬蟲監控任務"""
        config = load_config()
        crawler_config = config.get("ticket_crawler", {})
        
        if not crawler_config.get("enabled", False):
            logger.info("Ticket crawler monitoring is disabled")
            return
        
        self.enabled = True
        check_interval = crawler_config.get("check_interval", 300)  # 默認5分鐘
        
        # 創建監控任務
        self.monitor_task = tasks.loop(seconds=check_interval)(self.monitor_crawler_tickets)
        
        # 啟動任務
        self.monitor_task.start()
        logger.info(f"Started ticket crawler monitoring with interval of {check_interval} seconds")
    
    def update_interval(self, seconds: int):
        """更新監控間隔"""
        if self.monitor_task:
            self.monitor_task.change_interval(seconds=seconds)
            logger.info(f"Updated crawler monitoring interval to {seconds} seconds")
    
    def stop_monitoring(self):
        """停止監控任務"""
        if self.monitor_task:
            self.monitor_task.cancel()
            logger.info("Stopped ticket crawler monitoring task")
        self.enabled = False
    
    async def send_startup_notification(self):
        """發送啟動通知"""
        if not self.enabled:
            return
            
        config = load_config()
        # 使用專用的爬蟲頻道，如果沒有設定則使用主頻道
        channel_id = config.get("crawler_channel_id", config["discord_channel_id"])
        channel = self.bot.get_channel(channel_id)
        
        if not channel:
            logger.error(f"Error: Could not find crawler channel with ID {channel_id}")
            return
        
        crawler_config = config.get("ticket_crawler", {})
        total_urls = sum(len(site_config.get("urls", [])) for site_config in crawler_config.get("sites", {}).values())
        
        embed = discord.Embed(
            title="🕷️ 票券爬蟲監控已啟動",
            description="票券爬蟲監控功能已開始運行",
            color=discord.Color.orange(),
            timestamp=datetime.datetime.now(taiwan_tz)
        )
        
        embed.add_field(
            name="📊 監控狀態",
            value=f"正在監控 {total_urls} 個活動",
            inline=True
        )
        
        embed.add_field(
            name="⏱️ 檢查間隔",
            value=f"{crawler_config.get('check_interval', 300)} 秒",
            inline=True
        )
        
        embed.add_field(
            name="🌐 支持網站",
            value=", ".join(self.crawler.get_supported_sites()),
            inline=True
        )
        
        try:
            await channel.send(embed=embed)
            logger.info("Sent crawler startup notification to Discord")
        except Exception as e:
            logger.error(f"Failed to send crawler startup notification: {e}")
    
    async def monitor_crawler_tickets(self):
        """監控爬蟲票券可用性"""
        global previous_crawler_data, last_crawler_notification_time, crawler_notification_cooldown
        
        config = load_config()
        # 使用專用的爬蟲頻道，如果沒有設定則使用主頻道
        channel_id = config.get("crawler_channel_id", config["discord_channel_id"])
        channel = self.bot.get_channel(channel_id)

        if not channel:
            logger.error(f"Error: Could not find crawler channel with ID {channel_id}")
            return
        
        crawler_config = config.get("ticket_crawler", {})
        if not crawler_config.get("enabled", False):
            return
        
        # 檢查是否為首次運行
        first_run = self.monitor_task.current_loop == 0
        
        # 收集所有要監控的URL
        all_urls = []
        url_to_site = {}
        
        for site_name, site_config in crawler_config.get("sites", {}).items():
            if not site_config.get("enabled", False):
                continue
                
            urls = site_config.get("urls", [])
            for url in urls:
                all_urls.append(url)
                url_to_site[url] = site_name
        
        if not all_urls:
            logger.debug("No URLs to monitor")
            return
        
        # 清除不在配置中的URL的緩存數據
        self._cleanup_cache(all_urls)
        
        # 並發爬取所有URL
        logger.info(f"Starting crawler monitoring for {len(all_urls)} URLs")
        results = await self.crawler.crawl_multiple_events(all_urls)
        
        # 處理結果
        for i, (url, result) in enumerate(zip(all_urls, results)):
            site_name = url_to_site[url]
            
            # 跳過冷卻中的URL（除非是首次運行）
            if crawler_notification_cooldown.get(url, False) and not first_run:
                continue
            
            if not result.success:
                logger.warning(f"Failed to crawl {url}: {result.error_message}")
                continue
            
            # 檢查票券狀態變化
            await self._check_ticket_changes(
                url, site_name, result, channel, first_run, config
            )
    
    async def _check_ticket_changes(self, url: str, site_name: str, result: CrawlResult, 
                                   channel, first_run: bool, config: Dict[str, Any]):
        """檢查票券狀態變化並發送通知"""
        global previous_crawler_data, last_crawler_notification_time, crawler_notification_cooldown
        
        if not result.event_info or not result.ticket_areas:
            return
        
        # 計算當前可用票數
        current_available = sum(
            area.available_count or 0 
            for area in result.ticket_areas 
            if area.status == TicketStatus.AVAILABLE
        )
        
        # 獲取之前的數據
        previous_available = previous_crawler_data.get(url, 0)
        
        # 檢查是否需要發送通知
        should_notify = (
            first_run or  # 首次運行
            (previous_available == 0 and current_available > 0) or  # 從無票到有票
            (current_available > previous_available and previous_available > 0)  # 票數增加
        )
        
        if should_notify:
            # 檢查冷卻時間
            current_time = time.time()
            last_notification = last_crawler_notification_time.get(url, 0)
            cooldown_period = config.get("notification_cooldown", 3600)
            
            if config.get("enable_cooldown", True) and not first_run:
                if current_time - last_notification < cooldown_period:
                    logger.info(f"Skipping crawler notification for {result.event_info.title} due to cooldown")
                    return
            
            # 發送通知
            embed = self._create_crawler_notification_embed(result, site_name, first_run, previous_available)
            
            try:
                await channel.send(embed=embed)
                last_crawler_notification_time[url] = current_time
                
                # 設置冷卻
                if config.get("enable_cooldown", True):
                    crawler_notification_cooldown[url] = True
                    
                    # 安排冷卻重置
                    async def reset_cooldown():
                        await asyncio.sleep(cooldown_period)
                        crawler_notification_cooldown[url] = False
                        logger.info(f"Crawler cooldown reset for {result.event_info.title}")
                    
                    asyncio.create_task(reset_cooldown())
                
                logger.info(f"Sent crawler notification for {result.event_info.title} - {current_available} tickets available")
            except Exception as e:
                logger.error(f"Failed to send crawler notification: {e}")
        
        # 更新緩存數據
        previous_crawler_data[url] = current_available
    
    def _create_crawler_notification_embed(self, result: CrawlResult, site_name: str, 
                                         first_run: bool, previous_available: int) -> discord.Embed:
        """創建爬蟲通知嵌入消息"""
        event_info = result.event_info
        ticket_areas = result.ticket_areas
        
        # 計算總可用票數
        total_available = sum(
            area.available_count or 0 
            for area in ticket_areas 
            if area.status == TicketStatus.AVAILABLE
        )
        
        # 設置顏色和標題
        if total_available > 0:
            color = discord.Color.green()
            title = f"🎫 {event_info.title}"
        else:
            color = discord.Color.red()
            title = f"❌ {event_info.title}"
        
        embed = discord.Embed(
            title=title,
            description=f"**{event_info.date} {event_info.time}**\n📍 {event_info.venue}",
            color=color,
            timestamp=datetime.datetime.now(taiwan_tz)
        )
        
        # 添加票券信息
        available_areas = [area for area in ticket_areas if area.status == TicketStatus.AVAILABLE]
        sold_out_areas = [area for area in ticket_areas if area.status == TicketStatus.SOLD_OUT]
        
        if available_areas:
            area_text = []
            for area in available_areas[:10]:  # 限制顯示數量
                count_text = f"{area.available_count}張" if area.available_count else "有票"
                area_text.append(f"• {area.name} (${area.price}) - {count_text}")
            
            embed.add_field(
                name="✅ 有票區域",
                value="\n".join(area_text),
                inline=False
            )
        
        if sold_out_areas:
            area_text = []
            for area in sold_out_areas[:5]:  # 限制顯示數量
                area_text.append(f"• {area.name} (${area.price})")
            
            if len(sold_out_areas) > 5:
                area_text.append(f"• ... 還有 {len(sold_out_areas) - 5} 個售完區域")
            
            embed.add_field(
                name="❌ 售完區域",
                value="\n".join(area_text),
                inline=False
            )
        
        # 添加統計信息
        embed.add_field(
            name="📊 統計",
            value=f"總可用票數: {total_available}\n網站: {site_name.upper()}",
            inline=True
        )
        
        # 添加活動連結
        if event_info.url:
            embed.add_field(
                name="🔗 活動連結",
                value=f"[點此前往]({event_info.url})",
                inline=True
            )
        
        # 設置頁腳
        if first_run:
            embed.set_footer(text="🕷️ 爬蟲啟動通知 - 目前票券狀態")
        elif previous_available == 0:
            embed.set_footer(text="🎉 票券現已開放！(爬蟲檢測)")
        else:
            embed.set_footer(text="📈 票券數量增加！(爬蟲檢測)")
        
        return embed
    
    def _cleanup_cache(self, current_urls: List[str]):
        """清理不在當前配置中的URL的緩存數據"""
        global previous_crawler_data, last_crawler_notification_time, crawler_notification_cooldown
        
        for url in list(previous_crawler_data.keys()):
            if url not in current_urls:
                del previous_crawler_data[url]
                logger.info(f"Removed {url} from crawler previous_data cache")
        
        for url in list(last_crawler_notification_time.keys()):
            if url not in current_urls:
                del last_crawler_notification_time[url]
                logger.info(f"Removed {url} from crawler last_notification_time cache")
        
        for url in list(crawler_notification_cooldown.keys()):
            if url not in current_urls:
                del crawler_notification_cooldown[url]
                logger.info(f"Removed {url} from crawler notification_cooldown cache")
    
    def get_monitor_stats(self) -> Dict[str, Any]:
        """獲取監控統計信息"""
        config = load_config()
        crawler_config = config.get("ticket_crawler", {})
        
        total_urls = sum(
            len(site_config.get("urls", [])) 
            for site_config in crawler_config.get("sites", {}).values()
        )
        
        return {
            "enabled": self.enabled,
            "total_urls": total_urls,
            "supported_sites": self.crawler.get_supported_sites(),
            "crawler_stats": self.crawler.get_crawler_stats(),
            "cached_urls": len(previous_crawler_data),
        }
